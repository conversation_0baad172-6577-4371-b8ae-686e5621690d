name: CI/CD Pipeline

on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main, dev]
  workflow_dispatch:
    inputs:
      skip_e2e_for_deploy:
        description: 'Skip E2E tests for deployment (allows deployment even if E2E fails)'
        required: false
        default: false
        type: boolean
      force_deploy:
        description: 'Force deploy (skip all CI checks)'
        required: false
        default: false
        type: boolean

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint:
    name: ESLint Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup workspace
        uses: ./.github/actions/setup-workspace

      - name: Setup environment
        uses: ./.github/actions/setup-env
        with:
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}
          sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Run ESLint
        run: npm run lint

  type-check:
    name: TypeScript Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup workspace
        uses: ./.github/actions/setup-workspace

      - name: Setup environment
        uses: ./.github/actions/setup-env
        with:
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}
          sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Run TypeScript check
        run: npm run type-check

  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [lint, type-check]
    if: always() && (needs.lint.result == 'success' && needs.type-check.result == 'success') || github.event.inputs.force_deploy == 'true'
    outputs:
      release-version: ${{ steps.version.outputs.release_version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup workspace
        uses: ./.github/actions/setup-workspace
        with:
          setup-expo: true
          expo-token: ${{ secrets.EXPO_TOKEN }}

      - name: Setup environment
        uses: ./.github/actions/setup-env
        with:
          expo-token: ${{ secrets.EXPO_TOKEN }}
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}
          sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Generate release version
        id: version
        run: |
          RELEASE_VERSION="${GITHUB_REF#refs/heads/}-$(echo ${{ github.sha }} | cut -c1-7)"
          echo "release_version=$RELEASE_VERSION" >> $GITHUB_OUTPUT
          echo "Generated release version: $RELEASE_VERSION"

      - name: Build for verification (PR/push)
        if: github.event_name != 'workflow_dispatch'
        run: npx expo export --platform web --output-dir dist-check
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Build for production (manual deploy)
        if: github.event_name == 'workflow_dispatch'
        run: npx expo export --platform web
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Verify build artifacts
        run: |
          BUILD_DIR=$([ "${{ github.event_name }}" == "workflow_dispatch" ] && echo "dist" || echo "dist-check")
          if [ ! -f "$BUILD_DIR/index.html" ]; then
            echo "❌ index.html not found in build output"
            exit 1
          fi
          echo "✅ Build verification completed successfully"

      - name: Upload build artifacts (verification)
        if: github.event_name != 'workflow_dispatch'
        uses: actions/upload-artifact@v4
        with:
          name: web-build-verification
          path: dist-check/
          retention-days: 1

      - name: Upload build artifacts (production)
        if: github.event_name == 'workflow_dispatch'
        uses: actions/upload-artifact@v4
        with:
          name: production-build
          path: dist/
          retention-days: 7

  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name != 'workflow_dispatch' || (github.event_name == 'workflow_dispatch' && github.event.inputs.skip_e2e_for_deploy != 'true')
    timeout-minutes: 30
    container:
      image: mcr.microsoft.com/playwright:v1.53.0-jammy
      options: --ipc=host
    env:
      PLAYWRIGHT_BASE_URL: 'http://localhost:8081'
      CI: false
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup workspace
        uses: ./.github/actions/setup-workspace
        with:
          cache-key-suffix: -playwright

      - name: Setup environment
        uses: ./.github/actions/setup-env
        with:
          cache-key-suffix: -playwright
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}

      - name: Cache Playwright browsers
        uses: actions/cache@v4
        id: playwright-cache
        with:
          path: /home/<USER>/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ hashFiles('package-lock.json') }}

      - name: Install Playwright Browsers
        if: steps.playwright-cache.outputs.cache-hit != 'true'
        run: npx playwright install --with-deps

      - name: Start development server
        run: |
          echo "🚀 Starting Expo development server..."
          npx expo start --web --port 8081 --clear &

          # Wait for server to be ready
          for i in {1..30}; do
            if curl -s http://localhost:8081 > /dev/null 2>&1; then
              echo "✅ Development server is ready!"
              break
            fi
            echo "Waiting for server... ($i/30)"
            sleep 2
          done

          if ! curl -s http://localhost:8081 > /dev/null 2>&1; then
            echo "❌ Server failed to start"
            exit 1
          fi

      - name: Run Playwright tests
        run: npx playwright test --reporter=html,github

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 7
          if-no-files-found: ignore

  sentry:
    name: Create Sentry Release
    runs-on: ubuntu-latest
    needs: [build, e2e-tests]
    if: github.event_name == 'workflow_dispatch' && needs.build.result == 'success' && (needs.e2e-tests.result == 'success' || needs.e2e-tests.result == 'skipped' || github.event.inputs.skip_e2e_for_deploy == 'true')
    env:
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      SENTRY_ORG: kiflow
      SENTRY_PROJECT: react-native
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup workspace
        uses: ./.github/actions/setup-workspace

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: production-build
          path: dist/

      - name: Create and finalize Sentry release
        run: |
          RELEASE_VERSION="${{ needs.build.outputs.release-version }}"

          npx sentry-cli releases new $RELEASE_VERSION
          npx sentry-cli releases files $RELEASE_VERSION upload-sourcemaps dist/ --url-prefix ~/
          npx sentry-cli releases set-commits $RELEASE_VERSION --auto

          if [ "${GITHUB_REF#refs/heads/}" = "main" ]; then
            npx sentry-cli releases deploys $RELEASE_VERSION new -e production
          else
            npx sentry-cli releases deploys $RELEASE_VERSION new -e development
          fi

          npx sentry-cli releases finalize $RELEASE_VERSION

  deploy:
    name: Deploy to EAS
    runs-on: ubuntu-latest
    needs: [build, e2e-tests, sentry]
    if: github.event_name == 'workflow_dispatch' && needs.build.result == 'success' && needs.sentry.result == 'success' && (needs.e2e-tests.result == 'success' || needs.e2e-tests.result == 'skipped' || github.event.inputs.skip_e2e_for_deploy == 'true')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup workspace
        uses: ./.github/actions/setup-workspace
        with:
          setup-expo: true
          expo-token: ${{ secrets.EXPO_TOKEN }}

      - name: Setup environment
        uses: ./.github/actions/setup-env
        with:
          expo-token: ${{ secrets.EXPO_TOKEN }}
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          supabase-service-key: ${{ secrets.SUPABASE_SERVICE_KEY }}
          sentry-auth-token: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Deploy to EAS
        run: |
          echo "🚀 Deploying to EAS..."
          eas deploy --prod
          echo "✅ Deployment completed successfully!"

  summary:
    name: Pipeline Summary
    runs-on: ubuntu-latest
    needs: [lint, type-check, build, e2e-tests, sentry, deploy]
    if: always()
    steps:
      - name: Display summary
        run: |
          echo "=== CI/CD PIPELINE SUMMARY ==="
          echo "Trigger: ${{ github.event_name }}"
          echo "Branch: ${GITHUB_REF#refs/heads/}"
          echo "Commit: ${{ github.sha }}"
          echo ""

          # Display input flags if manual trigger
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "=== MANUAL TRIGGER OPTIONS ==="
            echo "Skip E2E for Deploy: ${{ github.event.inputs.skip_e2e_for_deploy }}"
            echo "Force Deploy: ${{ github.event.inputs.force_deploy }}"
            echo ""
          fi

          echo "=== JOB RESULTS ==="
          echo "Lint: ${{ needs.lint.result }}"
          echo "Type Check: ${{ needs.type-check.result }}"
          echo "Build: ${{ needs.build.result }}"
          echo "E2E Tests: ${{ needs.e2e-tests.result }}"
          echo "Sentry: ${{ needs.sentry.result }}"
          echo "Deploy: ${{ needs.deploy.result }}"
          echo ""

          # Determine overall status
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            # For manual deployments
            if [[ "${{ needs.deploy.result }}" == "success" ]]; then
              echo "🎉 DEPLOYMENT SUCCESSFUL!"
              echo "Release: ${{ needs.build.outputs.release-version }}"
              if [[ "${{ github.event.inputs.skip_e2e_for_deploy }}" == "true" ]]; then
                echo "⚠️  E2E tests were skipped for this deployment"
              fi
            elif [[ "${{ needs.deploy.result }}" == "skipped" ]]; then
              echo "⏭️  DEPLOYMENT SKIPPED"
              echo "Reason: Prerequisites not met or conditions not satisfied"
            else
              echo "❌ DEPLOYMENT FAILED"
              echo "Check the job logs for details"
            fi
          else
            # For CI runs (push/PR)
            if [[ "${{ needs.lint.result }}" == "success" &&
                  "${{ needs.type-check.result }}" == "success" &&
                  "${{ needs.build.result }}" == "success" &&
                  ("${{ needs.e2e-tests.result }}" == "success" || "${{ needs.e2e-tests.result }}" == "skipped") ]]; then
              echo "✅ ALL CI CHECKS PASSED"
            else
              echo "❌ CI PIPELINE FAILED"
              echo "One or more checks failed - see job details above"
            fi
          fi
