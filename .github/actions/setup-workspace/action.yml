name: 'Setup Workspace'
description: 'Setup Node.js, cache dependencies, and restore workspace dependencies'
inputs:
  node-version:
    description: 'Node.js version to use'
    required: false
    default: '22.14.0'
  cache-key-suffix:
    description: 'Additional suffix for cache key'
    required: false
    default: ''
  setup-expo:
    description: 'Whether to setup Expo CLI'
    required: false
    default: 'false'
    type: boolean
  expo-token:
    description: 'Expo authentication token'
    required: false

runs:
  using: 'composite'
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: 'npm'

    - name: Cache node_modules
      uses: actions/cache@v4
      id: node-modules-cache
      with:
        path: node_modules
        key: ${{ runner.os }}-node-modules-${{ hashFiles('package-lock.json') }}${{ inputs.cache-key-suffix }}
        restore-keys: |
          ${{ runner.os }}-node-modules-

    - name: Install dependencies
      if: steps.node-modules-cache.outputs.cache-hit != 'true'
      shell: bash
      run: npm ci

    - name: Setup Expo CLI
      if: inputs.setup-expo == 'true' && inputs.expo-token != ''
      uses: expo/expo-github-action@v8
      with:
        eas-version: latest
        token: ${{ inputs.expo-token }}

    - name: Log workspace status
      shell: bash
      run: |
        echo "=== Workspace Status ==="
        echo "Node modules cache hit: ${{ steps.node-modules-cache.outputs.cache-hit == 'true' }}"
        echo "Node modules: $([ -d node_modules ] && echo '✅ Available' || echo '❌ Missing')"
        echo "Expo setup: $([ '${{ inputs.setup-expo }}' == 'true' ] && echo '✅ Enabled' || echo '❌ Disabled')"
